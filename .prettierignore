dist
build
node_modules
*.min.js
*.min.css
coverage
.nyc_output
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist
.serverless
.fusebox
.dynamodb
.tern-port
.vscode-test
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.tsbuildinfo
bun.lockb
