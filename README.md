# NexusFlow

A comprehensive knowledge management application inspired by Microsoft Loop's design philosophy, offering seamless integration between different content types while providing powerful AI-assisted capabilities.

## ✨ Features

### Core Architecture
- **Fluid Components**: Seamless integration between different content types
- **Dual Sidebar Layout**: Inspired by Microsoft Loop with adaptive layout management
- **Real-time Collaboration**: Built-in CRDT support for conflict resolution
- **AI Integration**: Intelligent assistance throughout the application

### Supported Component Types
- 📄 **Documents** - Rich text editing with Plate.js
- 📋 **Kanban Boards** - Visual task management
- 📊 **Tables** - Structured data organization
- 🤖 **AI Chat** - Intelligent conversations
- 📅 **Calendar** - Event and schedule management
- ✅ **Task Manager** - Todo and project tracking
- 🧠 **Mind Maps** - Visual idea organization
- 🎨 **Whiteboard** - Collaborative drawing
- 📈 **Charts** - Data visualization
- 🕸️ **Knowledge Graph** - Connected information
- 🌐 **WebView** - Embedded web content

## 🚀 Technology Stack

### Frontend
- **React 19** + **TypeScript** - Modern React with latest features
- **Vite** + **Bun** - Ultra-fast build tooling
- **Tailwind CSS 4** - Utility-first styling with custom design system
- **Framer Motion** - Smooth animations and transitions
- **Zustand** - Lightweight state management
- **Plate.js** - Rich text editing (planned migration to ProseMirror)

### Backend (Planned)
- **Tauri 2** - Rust-based desktop application framework
- **SQLite3** - Metadata storage
- **RocksDB** - File-based document storage
- **CRDT** - Conflict-free replicated data types

## 🎨 Design System

The application features a comprehensive design system with:
- **Adaptive Themes** - Light/Dark mode with system preference detection
- **Custom Color Palette** - Carefully crafted color system
- **Fluid Layout** - Responsive three-column layout with resizable panels
- **Motion Design** - Thoughtful animations and micro-interactions

## 🛠️ Development

### Prerequisites
- **Node.js 22+**
- **Bun** (recommended) or npm/yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd nexusflow

# Install dependencies
bun install

# Start development server
bun run dev
```

### Available Scripts

```bash
# Development
bun run dev          # Start development server
bun run build        # Build for production
bun run preview      # Preview production build

# Code Quality
bun run lint         # Run ESLint
bun run lint-fix     # Fix ESLint issues
bun run format       # Format code with Prettier
bun run type-check   # TypeScript type checking

# Testing
bun run test         # Run tests
bun run test-ui      # Run tests with UI
```

## 📁 Project Structure

```
nexusflow/
├── src/
│   ├── @types/              # Global type declarations
│   ├── api/                 # API layer and services
│   ├── assets/              # Static assets (fonts, images)
│   ├── components/          # Reusable UI components
│   │   ├── ui/              # Basic UI components
│   │   ├── layout/          # Layout components
│   │   └── shared/          # Shared business components
│   ├── features/            # Feature-specific modules
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility libraries
│   ├── providers/           # Context providers
│   ├── routes/              # Routing configuration
│   ├── stores/              # Zustand state stores
│   ├── styles/              # Global styles and themes
│   └── views/               # Page-level components
├── tests/                   # Test files
└── ...config files
```

## 🎯 Key Features

### Layout System
- **Three-Column Layout**: Sidebar, Auxiliary Bar, and Editor Area
- **Resizable Panels**: Drag to resize with smooth animations
- **Adaptive Visibility**: Show/hide panels based on screen size and user preference
- **Keyboard Shortcuts**: Full keyboard navigation support

### Fluid Components
- **Modular Design**: Each component type is self-contained
- **Drag & Drop**: Rearrange components within documents
- **Real-time Updates**: Changes sync across all connected clients
- **Version History**: Track changes and revert when needed

### State Management
- **Layout State**: Panel visibility, sizes, and modes
- **Document State**: Content, components, and metadata
- **Theme State**: User preferences and custom themes
- **Persistent Storage**: Automatic saving to localStorage

## 🔧 Configuration

### Path Aliases
The project uses TypeScript path mapping for clean imports:

```typescript
import { Button } from '@components/ui/Button'
import { useLayoutStore } from '@stores/useLayoutStore'
import { cn } from '@lib/utils'
```

### Theme System
Custom CSS variables enable seamless theme switching:

```css
/* Light mode */
--color-text: 0 0 0 / 0.85;
--color-fill: 0 0 0 / 0.1;

/* Dark mode */
--color-text: 255 255 255 / 0.85;
--color-fill: 255 255 255 / 0.1;
```

## 🚧 Roadmap

- [ ] **Rich Text Editor**: Complete Plate.js integration
- [ ] **Real-time Collaboration**: WebSocket-based sync
- [ ] **Plugin System**: Extensible component architecture
- [ ] **Mobile Support**: Responsive design for tablets and phones
- [ ] **Desktop App**: Tauri 2 implementation
- [ ] **Cloud Sync**: Optional cloud storage integration
- [ ] **Advanced AI**: GPT integration for content assistance

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [Microsoft Loop](https://loop.cloud.microsoft.com)
- Built with modern web technologies
- Designed for productivity and collaboration
