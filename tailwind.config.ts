/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      // Custom colors using CSS variables
      colors: {
        // System colors
        red: 'rgb(var(--color-red) / <alpha-value>)',
        orange: 'rgb(var(--color-orange) / <alpha-value>)',
        yellow: 'rgb(var(--color-yellow) / <alpha-value>)',
        green: 'rgb(var(--color-green) / <alpha-value>)',
        mint: 'rgb(var(--color-mint) / <alpha-value>)',
        teal: 'rgb(var(--color-teal) / <alpha-value>)',
        cyan: 'rgb(var(--color-cyan) / <alpha-value>)',
        blue: 'rgb(var(--color-blue) / <alpha-value>)',
        indigo: 'rgb(var(--color-indigo) / <alpha-value>)',
        purple: 'rgb(var(--color-purple) / <alpha-value>)',
        pink: 'rgb(var(--color-pink) / <alpha-value>)',
        brown: 'rgb(var(--color-brown) / <alpha-value>)',
        gray: 'rgb(var(--color-gray) / <alpha-value>)',
        
        // Fill colors
        fill: {
          DEFAULT: 'var(--color-fill)',
          secondary: 'var(--color-fillSecondary)',
          tertiary: 'var(--color-fillTertiary)',
          quaternary: 'var(--color-fillQuaternary)',
          quinary: 'var(--color-fillQuinary)',
          vibrant: 'rgb(var(--color-fillVibrant) / <alpha-value>)',
          'vibrant-secondary': 'rgb(var(--color-fillVibrantSecondary) / <alpha-value>)',
          'vibrant-tertiary': 'rgb(var(--color-fillVibrantTertiary) / <alpha-value>)',
          'vibrant-quaternary': 'rgb(var(--color-fillVibrantQuaternary) / <alpha-value>)',
          'vibrant-quinary': 'rgb(var(--color-fillVibrantQuinary) / <alpha-value>)',
        },
        
        // Text colors
        text: {
          DEFAULT: 'var(--color-text)',
          secondary: 'var(--color-textSecondary)',
          tertiary: 'var(--color-textTertiary)',
          quaternary: 'var(--color-textQuaternary)',
          quinary: 'var(--color-textQuinary)',
          vibrant: 'rgb(var(--color-textVibrant) / <alpha-value>)',
          'vibrant-secondary': 'rgb(var(--color-textVibrantSecondary) / <alpha-value>)',
          'vibrant-tertiary': 'rgb(var(--color-textVibrantTertiary) / <alpha-value>)',
          'vibrant-quaternary': 'rgb(var(--color-textVibrantQuaternary) / <alpha-value>)',
          'vibrant-quinary': 'rgb(var(--color-textVibrantQuinary) / <alpha-value>)',
        },
        
        // Material colors
        material: {
          'ultra-thick': 'var(--color-materialUltraThick)',
          thick: 'var(--color-materialThick)',
          medium: 'var(--color-materialMedium)',
          thin: 'var(--color-materialThin)',
          'ultra-thin': 'var(--color-materialUltraThin)',
          opaque: 'rgb(var(--color-materialOpaque) / <alpha-value>)',
        },
        
        // Control colors
        control: {
          enabled: 'rgb(var(--color-controlEnabled) / <alpha-value>)',
          disabled: 'rgb(var(--color-controlDisabled) / <alpha-value>)',
        },
        
        // Interface colors
        menu: 'var(--color-menu)',
        popover: 'var(--color-popover)',
        titlebar: 'var(--color-titlebar)',
        sidebar: 'var(--color-sidebar)',
        'selection-focused': 'var(--color-selectionFocused)',
        'selection-focused-fill': 'rgb(var(--color-selectionFocusedFill) / <alpha-value>)',
        'selection-unfocused': 'var(--color-selectionUnfocused)',
        'selection-unfocused-fill': 'var(--color-selectionUnfocusedFill)',
        'header-view': 'var(--color-headerView)',
        tooltip: 'var(--color-tooltip)',
        'under-window-background': 'var(--color-underWindowBackground)',
      },
      
      // Typography
      fontFamily: {
        sans: [
          'SF Pro Display',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'Arial',
          'sans-serif'
        ],
        mono: [
          'SF Mono',
          'Monaco',
          'Inconsolata',
          'Roboto Mono',
          'source-code-pro',
          'Menlo',
          'monospace'
        ]
      },
      
      // Spacing for layout gaps
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // Animation and transitions
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      
      // Border radius for modern design
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // Backdrop blur
      backdropBlur: {
        xs: '2px',
      },
    },
  },
  plugins: [
    // Add any additional Tailwind plugins here
  ],
}

export default config
