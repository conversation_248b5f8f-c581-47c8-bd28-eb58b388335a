{"indentSize": 2, "tabSize": 2, "newLineCharacter": "\n", "convertTabsToSpaces": true, "insertSpaceAfterCommaDelimiter": true, "insertSpaceAfterSemicolonInForStatements": true, "insertSpaceBeforeAndAfterBinaryOperators": true, "insertSpaceAfterConstructor": false, "insertSpaceAfterKeywordsInControlFlowStatements": true, "insertSpaceAfterFunctionKeywordForAnonymousFunctions": false, "insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false, "insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false, "insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true, "insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": false, "insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": false, "insertSpaceAfterTypeAssertion": false, "insertSpaceBeforeFunctionParenthesis": false, "placeOpenBraceOnNewLineForFunctions": false, "placeOpenBraceOnNewLineForControlBlocks": false, "insertSpaceBeforeTypeAnnotation": false, "indentMultiLineObjectLiteralBeginningOnBlankLine": false, "semicolons": "remove"}