[install]
# Package installation configuration
registry = "https://registry.npmjs.org/"
cache = true
exact = false
production = false
optional = true
dev = true
peer = false
frozenLockfile = false

[install.scopes]
# Scoped package configuration
"@types" = { registry = "https://registry.npmjs.org/" }
"@tauri-apps" = { registry = "https://registry.npmjs.org/" }
"@udecode" = { registry = "https://registry.npmjs.org/" }

[run]
# Script execution configuration
shell = "system"
silent = false

[test]
# Test configuration
coverage = false
preload = []

[build]
# Build configuration
target = "browser"
format = "esm"
splitting = true
minify = true
sourcemap = true

[dev]
# Development server configuration
port = 3000
host = "localhost"
open = true
cors = true

[telemetry]
# Disable telemetry for privacy
disabled = true
