import { useEffect } from 'react'
import { useLayoutStore } from '@stores/useLayoutStore'
import { useDocumentStore } from '@stores/useDocumentStore'
import { SHORTCUTS } from '@lib/constants'

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  metaKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  action: () => void
  preventDefault?: boolean
}

const parseShortcut = (shortcut: string): Omit<KeyboardShortcut, 'action'> => {
  const parts = shortcut.toLowerCase().split('+')
  const key = parts[parts.length - 1]
  
  return {
    key,
    ctrlKey: parts.includes('ctrl'),
    metaKey: parts.includes('cmd'),
    shiftKey: parts.includes('shift'),
    altKey: parts.includes('alt'),
    preventDefault: true,
  }
}

const matchesShortcut = (event: KeyboardEvent, shortcut: Omit<KeyboardShortcut, 'action'>): boolean => {
  return (
    event.key.toLowerCase() === shortcut.key &&
    !!event.ctrlKey === !!shortcut.ctrlKey &&
    !!event.metaKey === !!shortcut.metaKey &&
    !!event.shiftKey === !!shortcut.shiftKey &&
    !!event.altKey === !!shortcut.altKey
  )
}

export const useKeyboardShortcuts = () => {
  const { 
    toggleSidebar, 
    toggleAuxiliary, 
    togglePanel 
  } = useLayoutStore()
  
  const { 
    createDocument, 
    setActiveDocument,
    getActiveDocument 
  } = useDocumentStore()

  useEffect(() => {
    const shortcuts: KeyboardShortcut[] = [
      // Global shortcuts
      {
        ...parseShortcut(SHORTCUTS.COMMAND_PALETTE),
        action: () => togglePanel(),
      },
      {
        ...parseShortcut(SHORTCUTS.NEW_DOCUMENT),
        action: () => {
          const docId = createDocument()
          setActiveDocument(docId)
        },
      },
      {
        ...parseShortcut(SHORTCUTS.SAVE),
        action: () => {
          // Auto-save is handled by the store, but we can trigger manual save here
          console.log('Manual save triggered')
        },
      },
      
      // Layout shortcuts
      {
        ...parseShortcut(SHORTCUTS.TOGGLE_SIDEBAR),
        action: () => toggleSidebar(),
      },
      {
        ...parseShortcut(SHORTCUTS.TOGGLE_AUXILIARY),
        action: () => toggleAuxiliary(),
      },
      
      // Navigation shortcuts
      {
        ...parseShortcut(SHORTCUTS.FOCUS_EDITOR),
        action: () => {
          // Focus the editor area
          const editorElement = document.querySelector('[data-editor-area]') as HTMLElement
          if (editorElement) {
            editorElement.focus()
          }
        },
      },
    ]

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      const target = event.target as HTMLElement
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        // Only allow certain global shortcuts in inputs
        const globalShortcuts = [SHORTCUTS.COMMAND_PALETTE, SHORTCUTS.SAVE]
        const currentShortcut = shortcuts.find(shortcut => matchesShortcut(event, shortcut))
        
        if (!currentShortcut || !globalShortcuts.includes(
          Object.values(SHORTCUTS).find(s => 
            parseShortcut(s).key === currentShortcut.key &&
            parseShortcut(s).metaKey === currentShortcut.metaKey &&
            parseShortcut(s).ctrlKey === currentShortcut.ctrlKey &&
            parseShortcut(s).shiftKey === currentShortcut.shiftKey
          ) || ''
        )) {
          return
        }
      }

      for (const shortcut of shortcuts) {
        if (matchesShortcut(event, shortcut)) {
          if (shortcut.preventDefault) {
            event.preventDefault()
          }
          shortcut.action()
          break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [toggleSidebar, toggleAuxiliary, togglePanel, createDocument, setActiveDocument])
}

export default useKeyboardShortcuts
