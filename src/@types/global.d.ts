// Global type declarations

declare global {
  interface Window {
    __DEV__: boolean
  }
}

// CSS Module declarations
declare module '*.module.css' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { [key: string]: string }
  export default classes
}

// Asset declarations
declare module '*.svg' {
  import React from 'react'
  const SVG: React.VFC<React.SVGProps<SVGSVGElement>>
  export default SVG
}

declare module '*.png' {
  const src: string
  export default src
}

declare module '*.jpg' {
  const src: string
  export default src
}

declare module '*.jpeg' {
  const src: string
  export default src
}

declare module '*.gif' {
  const src: string
  export default src
}

declare module '*.webp' {
  const src: string
  export default src
}

declare module '*.ico' {
  const src: string
  export default src
}

declare module '*.woff' {
  const src: string
  export default src
}

declare module '*.woff2' {
  const src: string
  export default src
}

// Environment variables
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test'
    VITE_APP_NAME?: string
    VITE_APP_VERSION?: string
    VITE_API_BASE_URL?: string
  }
}

// Extend HTMLElement for custom data attributes
declare global {
  interface HTMLElement {
    dataset: DOMStringMap & {
      editorArea?: string
      componentId?: string
      documentId?: string
    }
  }
}

export {}
