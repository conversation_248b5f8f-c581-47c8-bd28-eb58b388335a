/* Framer Motion helper classes and custom animations */

/* Layout transition helpers */
.layout-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.layout-transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Backdrop blur animations */
.backdrop-animate {
  transition: backdrop-filter 0.2s ease-in-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Focus states */
.focus-ring {
  outline: none;
}

.focus-ring:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

/* Loading animations */
.animate-pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Slide animations */
.slide-in-left {
  animation: slide-in-left 0.3s ease-out;
}

.slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

.slide-in-up {
  animation: slide-in-up 0.3s ease-out;
}

.slide-in-down {
  animation: slide-in-down 0.3s ease-out;
}
  
  @keyframes slide-in-left {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slide-in-right {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slide-in-up {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes slide-in-down {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* Scale animations */
  .scale-in {
    animation: scale-in 0.2s ease-out;
  }
  
  @keyframes scale-in {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  /* Fade animations */
  .fade-in {
    animation: fade-in 0.2s ease-in-out;
  }
  
  .fade-in-delayed {
    animation: fade-in 0.3s ease-in-out 0.1s both;
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  /* Shimmer effect for loading states */
  .shimmer {
    background: linear-gradient(
      90deg,
      rgb(243 244 246) 25%,
      rgb(229 231 235) 50%,
      rgb(243 244 246) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @media (prefers-color-scheme: dark) {
    .shimmer {
      background: linear-gradient(
        90deg,
        rgb(55 65 81) 25%,
        rgb(75 85 99) 50%,
        rgb(55 65 81) 75%
      );
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}
