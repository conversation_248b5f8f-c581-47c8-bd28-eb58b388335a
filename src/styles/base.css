@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for Theme System */
@media (prefers-color-scheme: light) {
  html {
    --color-red: 255 69 58;
    --color-orange: 255 149 0;
    --color-yellow: 255 204 0;
    --color-green: 40 205 65;
    --color-mint: 0 199 190;
    --color-teal: 89 173 196;
    --color-cyan: 85 190 240;
    --color-blue: 0 122 255;
    --color-indigo: 88 86 214;
    --color-purple: 175 82 222;
    --color-pink: 255 45 85;
    --color-brown: 162 132 94;
    --color-gray: 142 142 147;
    --color-fill: 0 0 0 / 0.1;
    --color-fillSecondary: 0 0 0 / 0.08;
    --color-fillTertiary: 0 0 0 / 0.05;
    --color-fillQuaternary: 0 0 0 / 0.03;
    --color-fillQuinary: 0 0 0 / 0.02;
    --color-fillVibrant: 217 217 217;
    --color-fillVibrantSecondary: 230 230 230;
    --color-fillVibrantTertiary: 242 242 242;
    --color-fillVibrantQuaternary: 247 247 247;
    --color-fillVibrantQuinary: 251 251 251;
    --color-text: 0 0 0 / 0.85;
    --color-textSecondary: 0 0 0 / 0.5;
    --color-textTertiary: 0 0 0 / 0.25;
    --color-textQuaternary: 0 0 0 / 0.1;
    --color-textQuinary: 0 0 0 / 0.05;
    --color-textVibrant: 76 76 76;
    --color-textVibrantSecondary: 128 128 128;
    --color-textVibrantTertiary: 191 191 191;
    --color-textVibrantQuaternary: 230 230 230;
    --color-textVibrantQuinary: 242 242 242;
    --color-materialUltraThick: 246 246 246 / 0.84;
    --color-materialThick: 246 246 246 / 0.72;
    --color-materialMedium: 246 246 246 / 0.6;
    --color-materialThin: 246 246 246 / 0.48;
    --color-materialUltraThin: 246 246 246 / 0.36;
    --color-materialOpaque: 246 246 246;
    --color-controlEnabled: 251 251 251;
    --color-controlDisabled: 243 243 243;
    --color-menu: 40 40 40 / 0.58;
    --color-popover: 0 0 0 / 0.28;
    --color-titlebar: 234 234 234 / 0.8;
    --color-sidebar: 234 234 234 / 0.84;
    --color-selectionFocused: 10 130 255 / 0.75;
    --color-selectionFocusedFill: 10 130 255;
    --color-selectionUnfocused: 0 0 0 / 0.1;
    --color-selectionUnfocusedFill: 246 246 246 / 0.84;
    --color-headerView: 255 255 255 / 0.8;
    --color-tooltip: 246 246 246 / 0.6;
    --color-underWindowBackground: 246 246 246 / 0.84;
  }
}

@media (prefers-color-scheme: dark) {
  html {
    --color-red: 255 69 58;
    --color-orange: 255 159 10;
    --color-yellow: 255 214 10;
    --color-green: 50 215 75;
    --color-mint: 106 196 220;
    --color-teal: 106 196 220;
    --color-cyan: 90 200 245;
    --color-blue: 10 132 255;
    --color-indigo: 94 92 230;
    --color-purple: 191 90 242;
    --color-pink: 255 55 95;
    --color-brown: 172 142 104;
    --color-gray: 152 152 157;
    --color-fill: 255 255 255 / 0.1;
    --color-fillSecondary: 255 255 255 / 0.08;
    --color-fillTertiary: 255 255 255 / 0.05;
    --color-fillQuaternary: 255 255 255 / 0.03;
    --color-fillQuinary: 255 255 255 / 0.02;
    --color-fillVibrant: 36 36 36;
    --color-fillVibrantSecondary: 20 20 20;
    --color-fillVibrantTertiary: 13 13 13;
    --color-fillVibrantQuaternary: 9 9 9;
    --color-fillVibrantQuinary: 7 7 7;
    --color-text: 255 255 255 / 0.85;
    --color-textSecondary: 255 255 255 / 0.5;
    --color-textTertiary: 255 255 255 / 0.25;
    --color-textQuaternary: 255 255 255 / 0.1;
    --color-textQuinary: 255 255 255 / 0.05;
    --color-textVibrant: 229 229 229;
    --color-textVibrantSecondary: 124 124 124;
    --color-textVibrantTertiary: 65 65 65;
    --color-textVibrantQuaternary: 35 35 35;
    --color-textVibrantQuinary: 17 17 17;
    --color-materialUltraThick: 40 40 40 / 0.84;
    --color-materialThick: 40 40 40 / 0.72;
    --color-materialMedium: 40 40 40 / 0.6;
    --color-materialThin: 40 40 40 / 0.48;
    --color-materialUltraThin: 40 40 40 / 0.36;
    --color-materialOpaque: 40 40 40;
    --color-controlEnabled: 255 255 255 / 0.2;
    --color-controlDisabled: 255 255 255 / 0.1;
    --color-menu: 246 246 246 / 0.72;
    --color-popover: 246 246 246 / 0.6;
    --color-titlebar: 60 60 60 / 0.8;
    --color-sidebar: 0 0 0 / 0.45;
    --color-selectionFocused: 10 130 255 / 0.75;
    --color-selectionFocusedFill: 10 130 255;
    --color-selectionUnfocused: 255 255 255 / 0.1;
    --color-selectionUnfocusedFill: 40 40 40 / 0.65;
    --color-headerView: 30 30 30 / 0.8;
    --color-tooltip: 0 0 0 / 0.35;
    --color-underWindowBackground: 0 0 0 / 0.45;
  }
}

/* Base styles */
@layer base {
  * {
    @apply border-fill-secondary;
  }
  
  body {
    @apply bg-fill-vibrant-quinary text-text font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Prevent page scrolling */
  html, body {
    @apply h-full overflow-hidden;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-fill-tertiary;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-fill-secondary rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-fill;
  }
}

/* Component styles */
@layer components {
  .layout-grid {
    @apply grid h-full w-full gap-3 p-3;
    grid-template-areas: 
      "sidebar auxiliary editor"
      "sidebar auxiliary editor";
    grid-template-columns: auto auto 1fr;
    grid-template-rows: 1fr;
  }
  
  .layout-sidebar {
    grid-area: sidebar;
    @apply bg-sidebar backdrop-blur-md rounded-2xl overflow-hidden;
  }
  
  .layout-auxiliary {
    grid-area: auxiliary;
    @apply bg-material-thick backdrop-blur-md rounded-2xl overflow-hidden;
  }
  
  .layout-editor {
    grid-area: editor;
    @apply bg-fill-vibrant-quinary rounded-2xl overflow-hidden;
  }
  
  .fluid-component {
    @apply bg-material-thin backdrop-blur-sm rounded-xl border border-fill-secondary;
  }
}
