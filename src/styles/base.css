@import "tailwindcss";

/* Base styles */
* {
  border-color: rgb(229 231 235);
}

body {
  background-color: white;
  color: rgb(17 24 39);
  font-family: system-ui, -apple-system, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

@media (prefers-color-scheme: dark) {
  * {
    border-color: rgb(55 65 81);
  }

  body {
    background-color: rgb(17 24 39);
    color: rgb(243 244 246);
  }
}

/* Prevent page scrolling */
html, body {
  height: 100%;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 0.5rem;
}

::-webkit-scrollbar-track {
  background-color: rgb(243 244 246);
}

::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background-color: rgb(31 41 55);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Component styles */
.layout-grid {
  display: grid;
  height: 100%;
  width: 100%;
  gap: 0.75rem;
  padding: 0.75rem;
  grid-template-areas:
    "sidebar auxiliary editor"
    "sidebar auxiliary editor";
  grid-template-columns: auto auto 1fr;
  grid-template-rows: 1fr;
}

.layout-sidebar {
  grid-area: sidebar;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid rgb(229 231 235);
}

.layout-auxiliary {
  grid-area: auxiliary;
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid rgb(229 231 235);
}

.layout-editor {
  grid-area: editor;
  background-color: white;
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid rgb(229 231 235);
}

.fluid-component {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(4px);
  border-radius: 0.75rem;
  border: 1px solid rgb(229 231 235);
}

@media (prefers-color-scheme: dark) {
  .layout-sidebar {
    background-color: rgba(17, 24, 39, 0.8);
    border-color: rgb(55 65 81);
  }

  .layout-auxiliary {
    background-color: rgba(17, 24, 39, 0.6);
    border-color: rgb(55 65 81);
  }

  .layout-editor {
    background-color: rgb(17 24 39);
    border-color: rgb(55 65 81);
  }

  .fluid-component {
    background-color: rgba(17, 24, 39, 0.4);
    border-color: rgb(55 65 81);
  }
}
