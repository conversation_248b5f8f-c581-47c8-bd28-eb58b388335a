import React from 'react'
import { motion } from 'framer-motion'
import { useLayoutStore } from '@stores/useLayoutStore'
import { Sidebar } from './Sidebar'
import { AuxiliaryBar } from './AuxiliaryBar'
import { EditorArea } from './EditorArea'
import { Panel } from './Panel'
import { cn } from '@lib/utils'

export const MainLayout: React.FC = React.memo(() => {
  const {
    sidebarVisible,
    auxiliaryVisible,
    panelVisible,
    sidebarWidth,
    auxiliaryWidth,
  } = useLayoutStore()

  const gridTemplateColumns = React.useMemo(() => {
    const columns = []

    if (sidebarVisible) {
      columns.push(`${sidebarWidth}px`)
    }

    if (auxiliaryVisible) {
      columns.push(`${auxiliaryWidth}px`)
    }

    columns.push('1fr')

    return columns.join(' ')
  }, [sidebarVisible, auxiliaryVisible, sidebarWidth, auxiliaryWidth])

  const gridTemplateAreas = React.useMemo(() => {
    let areas = ''

    if (sidebarVisible && auxiliaryVisible) {
      areas = '"sidebar auxiliary editor"'
    } else if (sidebarVisible) {
      areas = '"sidebar editor"'
    } else if (auxiliaryVisible) {
      areas = '"auxiliary editor"'
    } else {
      areas = '"editor"'
    }

    return areas
  }, [sidebarVisible, auxiliaryVisible])

  return (
    <div className="h-full w-full overflow-hidden bg-under-window-background">
      <motion.div
        className={cn(
          'layout-grid',
          'h-full w-full gap-3 p-3'
        )}
        style={{
          gridTemplateColumns,
          gridTemplateAreas,
        }}
        layout
        transition={{
          duration: 0.3,
          ease: [0.4, 0, 0.2, 1],
        }}
      >
        {/* Sidebar */}
        {sidebarVisible && (
          <motion.div
            className="layout-sidebar"
            style={{ gridArea: 'sidebar' }}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
          >
            <Sidebar />
          </motion.div>
        )}

        {/* Auxiliary Bar */}
        {auxiliaryVisible && (
          <motion.div
            className="layout-auxiliary"
            style={{ gridArea: 'auxiliary' }}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2, delay: 0.05 }}
          >
            <AuxiliaryBar />
          </motion.div>
        )}

        {/* Editor Area */}
        <motion.div
          className="layout-editor"
          style={{ gridArea: 'editor' }}
          layout
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        >
          <EditorArea />
        </motion.div>
      </motion.div>

      {/* Command Panel */}
      {panelVisible && <Panel />}
    </div>
  )
})

MainLayout.displayName = 'MainLayout'
