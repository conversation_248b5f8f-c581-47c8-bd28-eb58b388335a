import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLayoutStore } from '@stores/useLayoutStore'
import { useDocumentStore } from '@stores/useDocumentStore'
import { Button } from '@components/ui/Button'
import { Input } from '@components/ui/Input'
import { cn } from '@lib/utils'

interface PanelProps {
  className?: string
}

interface Command {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  action: () => void
  shortcut?: string
}

export const Panel: React.FC<PanelProps> = React.memo(({ className }) => {
  const { togglePanel } = useLayoutStore()
  const { createDocument, setActiveDocument, documents } = useDocumentStore()
  
  const [query, setQuery] = React.useState('')
  const [selectedIndex, setSelectedIndex] = React.useState(0)

  const commands: Command[] = React.useMemo(() => [
    {
      id: 'new-document',
      title: 'New Document',
      description: 'Create a new document',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      action: () => {
        const docId = createDocument()
        setActiveDocument(docId)
        togglePanel()
      },
      shortcut: '⌘N'
    },
    {
      id: 'toggle-sidebar',
      title: 'Toggle Sidebar',
      description: 'Show or hide the sidebar',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      ),
      action: () => {
        useLayoutStore.getState().toggleSidebar()
        togglePanel()
      },
      shortcut: '⌘B'
    },
    {
      id: 'toggle-auxiliary',
      title: 'Toggle Auxiliary Bar',
      description: 'Show or hide the auxiliary bar',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      action: () => {
        useLayoutStore.getState().toggleAuxiliary()
        togglePanel()
      },
      shortcut: '⌘⇧B'
    },
    ...Object.values(documents).map(doc => ({
      id: `open-${doc.id}`,
      title: `Open: ${doc.title}`,
      description: 'Open this document',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      action: () => {
        setActiveDocument(doc.id)
        togglePanel()
      }
    }))
  ], [documents, createDocument, setActiveDocument, togglePanel])

  const filteredCommands = React.useMemo(() => {
    if (!query.trim()) return commands
    
    return commands.filter(command =>
      command.title.toLowerCase().includes(query.toLowerCase()) ||
      command.description.toLowerCase().includes(query.toLowerCase())
    )
  }, [commands, query])

  React.useEffect(() => {
    setSelectedIndex(0)
  }, [filteredCommands])

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < filteredCommands.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredCommands.length - 1
        )
      } else if (e.key === 'Enter') {
        e.preventDefault()
        if (filteredCommands[selectedIndex]) {
          filteredCommands[selectedIndex].action()
        }
      } else if (e.key === 'Escape') {
        togglePanel()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [filteredCommands, selectedIndex, togglePanel])

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-start justify-center bg-popover backdrop-blur-sm pt-[20vh]"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            togglePanel()
          }
        }}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className={cn(
            'w-full max-w-2xl mx-4 bg-material-thick backdrop-blur-md border border-fill-secondary rounded-2xl shadow-lg overflow-hidden',
            className
          )}
        >
          {/* Search Input */}
          <div className="p-4 border-b border-fill-secondary">
            <Input
              placeholder="Type a command or search..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="w-full text-lg border-none bg-transparent focus:ring-0"
              autoFocus
            />
          </div>

          {/* Commands List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="p-8 text-center text-text-tertiary">
                No commands found
              </div>
            ) : (
              <div className="p-2">
                {filteredCommands.map((command, index) => (
                  <motion.div
                    key={command.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.02 }}
                    className={cn(
                      'flex items-center p-3 rounded-lg cursor-pointer transition-colors',
                      index === selectedIndex 
                        ? 'bg-selection-focused-fill/10 border border-selection-focused-fill/20' 
                        : 'hover:bg-fill-secondary'
                    )}
                    onClick={command.action}
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-fill-secondary mr-3">
                      {command.icon}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-text truncate">
                        {command.title}
                      </div>
                      <div className="text-sm text-text-secondary truncate">
                        {command.description}
                      </div>
                    </div>
                    
                    {command.shortcut && (
                      <div className="text-xs text-text-tertiary bg-fill-tertiary px-2 py-1 rounded">
                        {command.shortcut}
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-fill-secondary bg-fill-vibrant-quinary">
            <div className="flex items-center justify-between text-xs text-text-tertiary">
              <div className="flex items-center space-x-4">
                <span>↑↓ to navigate</span>
                <span>↵ to select</span>
                <span>esc to close</span>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
})

Panel.displayName = 'Panel'
