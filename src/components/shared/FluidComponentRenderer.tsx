import React from 'react'
import { motion } from 'framer-motion'
import { FluidComponent } from '@stores/useDocumentStore'
import { useDocumentStore } from '@stores/useDocumentStore'
import { Button } from '@components/ui/Button'
import { Input } from '@components/ui/Input'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { FLUID_COMPONENT_TYPES } from '@lib/constants'
import { cn } from '@lib/utils'

interface FluidComponentRendererProps {
  component: FluidComponent
  documentId: string
  className?: string
}

export const FluidComponentRenderer: React.FC<FluidComponentRendererProps> = React.memo(({
  component,
  documentId,
  className
}) => {
  const { updateComponent, removeComponent } = useDocumentStore()
  const [isEditing, setIsEditing] = React.useState(false)
  const [title, setTitle] = React.useState(component.title)

  const handleTitleSubmit = () => {
    if (title.trim()) {
      updateComponent(documentId, component.id, { title: title.trim() })
      setIsEditing(false)
    }
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSubmit()
    } else if (e.key === 'Escape') {
      setTitle(component.title)
      setIsEditing(false)
    }
  }

  const handleDelete = () => {
    removeComponent(documentId, component.id)
  }

  const renderComponentContent = () => {
    switch (component.type) {
      case FLUID_COMPONENT_TYPES.DOCUMENT:
        return <DocumentComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.KANBAN:
        return <KanbanComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.TABLE:
        return <TableComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.AI_CHAT:
        return <AIChatComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.CALENDAR:
        return <CalendarComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.TASK_MANAGER:
        return <TaskManagerComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.MIND_MAP:
        return <MindMapComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.WHITEBOARD:
        return <WhiteboardComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.CHARTS:
        return <ChartsComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.KNOWLEDGE_GRAPH:
        return <KnowledgeGraphComponent component={component} documentId={documentId} />
      
      case FLUID_COMPONENT_TYPES.WEBVIEW:
        return <WebviewComponent component={component} documentId={documentId} />
      
      default:
        return <div className="p-4 text-text-tertiary">Unknown component type</div>
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn('fluid-component group', className)}
    >
      {/* Component Header */}
      <div className="flex items-center justify-between p-4 border-b border-fill-secondary">
        <div className="flex-1">
          {isEditing ? (
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onBlur={handleTitleSubmit}
              onKeyDown={handleTitleKeyDown}
              className="font-medium border-none bg-transparent p-0 focus:ring-0"
              autoFocus
            />
          ) : (
            <h3 
              className="font-medium text-text cursor-pointer hover:text-text-secondary transition-colors"
              onClick={() => setIsEditing(true)}
            >
              {component.title}
            </h3>
          )}
        </div>
        
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </Button>
          
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </Button>
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8 text-red hover:text-red hover:bg-red/10"
            onClick={handleDelete}
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </Button>
        </div>
      </div>

      {/* Component Content */}
      <div className="min-h-[200px]">
        {renderComponentContent()}
      </div>
    </motion.div>
  )
})

// Individual component implementations
const DocumentComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="prose prose-sm max-w-none">
      <div className="min-h-[150px] p-4 border border-dashed border-fill-secondary rounded-lg text-text-tertiary">
        Rich text editor would go here (Plate.js integration)
      </div>
    </div>
  </div>
)

const KanbanComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="grid grid-cols-3 gap-4">
      {['To Do', 'In Progress', 'Done'].map((column) => (
        <div key={column} className="bg-fill-vibrant-tertiary rounded-lg p-3">
          <h4 className="font-medium text-text mb-3">{column}</h4>
          <div className="space-y-2">
            <div className="bg-fill-vibrant-quinary p-2 rounded text-sm">Sample task</div>
          </div>
        </div>
      ))}
    </div>
  </div>
)

const TableComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b border-fill-secondary">
            <th className="text-left p-2 text-text">Column 1</th>
            <th className="text-left p-2 text-text">Column 2</th>
            <th className="text-left p-2 text-text">Column 3</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-fill-tertiary">
            <td className="p-2 text-text-secondary">Sample data</td>
            <td className="p-2 text-text-secondary">Sample data</td>
            <td className="p-2 text-text-secondary">Sample data</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
)

const AIChatComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="space-y-3 mb-4">
      <div className="flex justify-end">
        <div className="bg-blue text-white p-3 rounded-lg max-w-xs">
          Hello, how can you help me today?
        </div>
      </div>
      <div className="flex justify-start">
        <div className="bg-fill-vibrant-tertiary p-3 rounded-lg max-w-xs">
          I'm here to help! What would you like to know?
        </div>
      </div>
    </div>
    <div className="flex space-x-2">
      <Input placeholder="Type your message..." className="flex-1" />
      <Button>Send</Button>
    </div>
  </div>
)

const CalendarComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Calendar component placeholder
    </div>
  </div>
)

const TaskManagerComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Task manager component placeholder
    </div>
  </div>
)

const MindMapComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Mind map component placeholder
    </div>
  </div>
)

const WhiteboardComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Whiteboard component placeholder
    </div>
  </div>
)

const ChartsComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Charts component placeholder
    </div>
  </div>
)

const KnowledgeGraphComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Knowledge graph component placeholder
    </div>
  </div>
)

const WebviewComponent: React.FC<{ component: FluidComponent; documentId: string }> = ({ component }) => (
  <div className="p-4">
    <div className="text-center text-text-tertiary">
      Webview component placeholder
    </div>
  </div>
)

FluidComponentRenderer.displayName = 'FluidComponentRenderer'
