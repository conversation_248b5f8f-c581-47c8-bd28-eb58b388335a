{"name": "nexusflow", "version": "0.1.0", "description": "A comprehensive knowledge management application inspired by Microsoft Loop", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint-fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "format:ts": "tsfmt --replace src/**/*.ts src/**/*.tsx", "format:rust": "cd src-tauri && cargo fmt", "type-check": "tsc --noEmit", "prepare": "husky", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:info": "tauri info", "tauri:deps": "tauri deps", "rust:check": "cd src-tauri && cargo check", "rust:clippy": "cd src-tauri && cargo clippy", "rust:test": "cd src-tauri && cargo test"}, "dependencies": {"@udecode/plate-basic-elements": "^49.0.0", "@udecode/plate-basic-marks": "^49.0.0", "@udecode/plate-common": "^42.0.0", "@udecode/plate-ui": "^35.0.0", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^2.5.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.0.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "^2.5.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "husky": "^9.1.7", "lint-staged": "^16.1.2", "node": "^24.2.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "tailwindcss": "^4.0.0", "typescript": "^5.8.3", "typescript-formatter": "^7.2.2", "vite": "^6.3.5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}