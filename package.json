{"name": "nexusflow", "version": "0.1.0", "description": "A comprehensive knowledge management application inspired by Microsoft Loop", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint-fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "framer-motion": "^12.18.1", "zustand": "^5.0.5", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^16.2.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5"}}