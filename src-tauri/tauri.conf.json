{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "NexusFlow", "version": "0.1.0", "identifier": "com.nexusflow.app", "build": {"beforeDevCommand": "bun run dev", "beforeBuildCommand": "bun run build", "frontendDist": "../dist", "devUrl": "http://localhost:3000"}, "app": {"windows": [{"title": "NexusFlow", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "alwaysOnTop": false, "contentProtected": false, "skipTaskbar": false, "theme": "auto", "titleBarStyle": "visible", "hiddenTitle": false, "acceptFirstMouse": false, "tabbingIdentifier": "main"}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost; style-src 'self' 'unsafe-inline'; font-src 'self'; script-src 'self' 'unsafe-eval'"}, "macOSPrivateApi": false}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "", "category": "Productivity", "shortDescription": "A comprehensive knowledge management application", "longDescription": "NexusFlow is a comprehensive knowledge management application inspired by Microsoft Loop's design philosophy, offering seamless integration between different content types while providing powerful AI-assisted capabilities.", "deb": {"depends": []}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "plugins": {"updater": {"active": false}}}