<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="A comprehensive knowledge management application inspired by Microsoft Loop" />
    <title>NexusFlow</title>
    
    <!-- Preload critical fonts -->
    <link rel="preload" href="/assets/fonts/sf-pro-display.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <style>
      html {
        visibility: hidden;
        opacity: 0;
      }
      
      html.loaded {
        visibility: visible;
        opacity: 1;
        transition: opacity 0.2s ease-in-out;
      }
      
      /* Loading screen */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f7f7f7;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      @media (prefers-color-scheme: dark) {
        #loading {
          background: #1a1a1a;
        }
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e5e5;
        border-top: 3px solid #007aff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @media (prefers-color-scheme: dark) {
        .loading-spinner {
          border-color: #333;
          border-top-color: #007aff;
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- App root -->
    <div id="root"></div>
    
    <!-- Remove loading screen and show app -->
    <script>
      window.addEventListener('load', () => {
        const loading = document.getElementById('loading')
        const html = document.documentElement
        
        if (loading) {
          loading.style.opacity = '0'
          setTimeout(() => {
            loading.remove()
            html.classList.add('loaded')
          }, 200)
        }
      })
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
